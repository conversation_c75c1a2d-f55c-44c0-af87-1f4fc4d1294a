# Master-Slave Flow Architecture Documentation

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Current System Analysis](#current-system-analysis)
3. [AI Intent Classification](#ai-intent-classification)
4. [Master-Slave Flow Approaches](#master-slave-flow-approaches)
5. [Practical Implementation Example](#practical-implementation-example)
6. [Flow Creation with AI Intent Triggers](#flow-creation-with-ai-intent-triggers)
7. [Configuration Examples](#configuration-examples)
8. [Benefits and Recommendations](#benefits-and-recommendations)

## Executive Summary

This document outlines comprehensive strategies for implementing a master-slave flow architecture in Zaptra, where a master flow receives all messages and intelligently routes them to appropriate slave flows using AI intent classification. The system enables dynamic flow triggering based on message analysis rather than simple keyword matching.

## Current System Analysis

### Existing Components
- **FlowRouter Service**: Intelligent flow selection using AI intent classification
- **IntentClassifier Service**: AI-powered message intent detection with keyword + AI fallback
- **ConversationStateManager**: Manages conversation context and flow history
- **Flow Model**: Contains priority, flow_type, trigger_conditions, and is_active fields
- **Contact State System**: Tracks conversation state per contact
- **Node Types**: 20+ different node types including triggers, actions, and control flow

### Recent Changes Made (12 Changes)
1. Priority-based Flow Selection
2. AI Intent Classification Implementation
3. Flow Type Categorization
4. Trigger Conditions (JSON field)
5. Conversation State Management
6. Intelligent Flow Routing
7. Intent Change Detection
8. Flow History Tracking
9. Active Flow Management
10. Context Preservation
11. Fallback Mechanisms
12. Database Structure Updates

## AI Intent Classification

### How AI Intent Classification Works

The AI Intent Classification system uses a two-tier approach:

#### Tier 1: Keyword-Based Classification (Fast)
```php
private function classifyByKeywords(string $message): string
{
    $message = strtolower($message);
    
    // Agent/Support requests
    $agentKeywords = ['agent', 'human', 'support', 'help me', 'speak to someone'];
    foreach ($agentKeywords as $keyword) {
        if (stripos($message, $keyword) !== false) {
            return 'agent_request';
        }
    }
    
    // Group assignment requests
    $groupKeywords = ['group', 'join', 'add me', 'vip', 'premium', 'membership'];
    foreach ($groupKeywords as $keyword) {
        if (stripos($message, $keyword) !== false) {
            return 'group_assignment';
        }
    }
    
    return 'unknown';
}
```

#### Tier 2: AI-Powered Classification (Accurate)
```php
private function classifyWithAI(string $message, Contact $contact): string
{
    $systemPrompt = "You are an intent classification system for a customer service chatbot.
    
    Classify user messages into one of these intents:
    - agent_request: User wants to speak to a human agent/support
    - group_assignment: User wants to join a group, get VIP access, or membership
    - company_info: User asking about company, services, or general information
    - greeting: Simple greetings and pleasantries
    - general_inquiry: General questions or conversations";
    
    $response = Http::post('https://openrouter.ai/api/v1/chat/completions', [
        'model' => 'openai/gpt-4o-mini',
        'messages' => [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $message]
        ],
        'temperature' => 0.1,
        'response_format' => ['type' => 'json_object']
    ]);
    
    $result = json_decode($response->json()['choices'][0]['message']['content'], true);
    return $result['intent'] ?? 'unknown';
}
```

### Available Intents
- `agent_request`: User wants human support
- `group_assignment`: User wants to join groups/memberships
- `company_info`: Questions about company/services
- `greeting`: Simple greetings
- `general_inquiry`: General conversations

## Master-Slave Flow Approaches

### Approach 1: Master Flow as Router (Recommended)

**Concept**: Create a special "Master Flow" that acts as the central router for all incoming messages.

```
Master Flow Structure:
┌─────────────────┐
│   Start Node    │ (incomingMessage)
└─────────┬───────┘
          │
┌─────────▼───────┐
│ AI Intent Node  │ (Custom: analyzes message intent)
└─────────┬───────┘
          │
┌─────────▼───────┐
│  Router Node    │ (Custom: routes to slave flows)
└─────┬───┬───┬───┘
      │   │   │
   ┌──▼┐ ┌▼─┐ ┌▼──┐
   │S1 │ │S2│ │S3 │ (Trigger slave flows)
   └───┘ └──┘ └───┘
```

**Technical Details**:
- Create new node types: `AIIntentNode` and `FlowRouterNode`
- Master flow has highest priority (priority = 1)
- All other flows have lower priorities and specific trigger conditions
- Master flow uses AI to classify intent and route to appropriate slave flow
- Slave flows are triggered programmatically, not by message matching

**Pros**:
- Visual representation in flow builder
- Easy to understand and modify
- Centralized routing logic
- Can handle complex routing rules

**Cons**:
- Requires new custom node types
- More complex to implement initially

### Approach 2: Service-Based Master Router (Current Enhanced)

**Concept**: Enhance the existing FlowRouter service to act as a master router with designated master and slave flows.

**Implementation**:
```php
class MasterSlaveFlowRouter extends FlowRouter
{
    public function selectBestFlow($message, Contact $contact): ?Flow
    {
        // 1. Always check master flow first
        $masterFlow = $this->getMasterFlow();
        if ($masterFlow) {
            return $this->routeThroughMaster($masterFlow, $message, $contact);
        }

        // 2. Fallback to current logic
        return parent::selectBestFlow($message, $contact);
    }

    private function routeThroughMaster(Flow $masterFlow, $message, Contact $contact): ?Flow
    {
        // Master flow decides which slave flow to trigger
        $intent = $this->intentClassifier->classifyIntent($message->value, $contact);
        $slaveFlow = $this->findSlaveFlowForIntent($intent);

        if ($slaveFlow) {
            // Set master flow context but execute slave flow
            $this->conversationStateManager->setMasterFlow($contact, $masterFlow);
            $this->conversationStateManager->setCurrentFlow($contact, $slaveFlow);
            return $slaveFlow;
        }

        return $masterFlow; // Execute master flow if no slave found
    }
}
```

**Technical Details**:
- Add `is_master` boolean field to flows table
- Add `master_flow_id` field to flows table for slave flows
- Enhance ConversationStateManager to track master-slave relationships
- Master flow always gets first chance to process messages
- Master flow can delegate to slave flows or handle directly

**Pros**:
- Builds on existing architecture
- No new node types needed
- Flexible routing logic
- Easy to implement

**Cons**:
- Less visual in flow builder
- Routing logic hidden in service layer

### Approach 3: Hierarchical Flow System

**Concept**: Create a tree-like hierarchy where flows can have parent-child relationships.

**Implementation**:
```
Database Schema:
flows table:
- id
- parent_flow_id (nullable)
- flow_level (0=master, 1=slave, 2=sub-slave)
- delegation_rules (JSON)

Flow Hierarchy:
Master Flow (Level 0)
├── Customer Support Flow (Level 1)
│   ├── Technical Support (Level 2)
│   └── Billing Support (Level 2)
├── Sales Flow (Level 1)
│   ├── Product Demo (Level 2)
│   └── Pricing Inquiry (Level 2)
└── General Info Flow (Level 1)
```

**Technical Details**:
- Add parent_flow_id and flow_level to flows table
- Create FlowHierarchyManager service
- Messages bubble up from child to parent if not handled
- Parent flows can delegate to children based on rules

**Code Example**:
```php
class FlowHierarchyManager
{
    public function processMessage($message, Contact $contact): ?Flow
    {
        // Start with master flow (level 0)
        $currentFlow = $this->getMasterFlow();

        while ($currentFlow) {
            $result = $currentFlow->processMessage($message, $contact);

            if ($result->isHandled()) {
                return $currentFlow;
            }

            // Delegate to child flows
            $childFlow = $this->findBestChildFlow($currentFlow, $message, $contact);
            if ($childFlow) {
                $currentFlow = $childFlow;
                continue;
            }

            // Bubble up to parent if no child can handle
            $currentFlow = $currentFlow->parent_flow;
        }

        return null;
    }
}
```

**Pros**:
- Supports multi-level hierarchies
- Clear parent-child relationships
- Scalable for complex organizations
- Natural delegation patterns

**Cons**:
- More complex database structure
- Potential for circular references
- Harder to visualize
- Performance overhead with deep hierarchies

### Approach 4: Intent-Based Flow Orchestration

**Concept**: Use AI intent classification as the primary routing mechanism with flows subscribing to specific intents.

**Implementation**:
```php
class IntentOrchestrator
{
    private $intentFlowMapping = [
        'agent_request' => [
            'priority' => 1,
            'flows' => ['support_flow', 'escalation_flow'],
            'conditions' => ['business_hours' => true]
        ],
        'product_inquiry' => [
            'priority' => 2,
            'flows' => ['sales_flow', 'demo_flow'],
            'conditions' => ['user_type' => 'prospect']
        ],
        'general_question' => [
            'priority' => 3,
            'flows' => ['faq_flow', 'general_flow'],
            'conditions' => []
        ]
    ];

    public function routeByIntent($message, Contact $contact): ?Flow
    {
        $intent = $this->classifyIntent($message, $contact);
        $intentConfig = $this->intentFlowMapping[$intent] ?? null;

        if (!$intentConfig) {
            return $this->getDefaultFlow();
        }

        // Check conditions
        if (!$this->checkConditions($intentConfig['conditions'], $contact)) {
            return $this->getFallbackFlow($intent);
        }

        // Select best flow from candidates
        $flowCandidates = $intentConfig['flows'];
        return $this->selectBestFlowFromCandidates($flowCandidates, $message, $contact);
    }

    private function checkConditions(array $conditions, Contact $contact): bool
    {
        foreach ($conditions as $key => $value) {
            if ($key === 'business_hours') {
                if ($value && !$this->isBusinessHours()) {
                    return false;
                }
            } elseif ($key === 'user_type') {
                if ($contact->user_type !== $value) {
                    return false;
                }
            }
        }
        return true;
    }
}
```

**Technical Details**:
- Enhance IntentClassifier with more granular intents
- Create intent-to-flow mapping configuration
- Flows register for specific intents
- Master orchestrator manages intent-based routing
- Support for conditional routing based on context

**Pros**:
- AI-driven routing
- Easy to add new intents
- Flexible flow assignment
- Context-aware routing
- Highly configurable

**Cons**:
- Dependent on AI accuracy
- May miss edge cases
- Requires intent training
- Complex configuration management

### Approach 5: Event-Driven Flow System

**Concept**: Implement an event-driven architecture where flows subscribe to message events.

**Implementation**:
```php
class FlowEventDispatcher
{
    private $eventSubscribers = [];

    public function __construct()
    {
        $this->eventSubscribers = [
            'message.received' => [
                'MasterFlow' => ['priority' => 1, 'always' => true],
                'SupportFlow' => ['priority' => 2, 'conditions' => ['intent' => 'agent_request']],
                'SalesFlow' => ['priority' => 3, 'conditions' => ['intent' => 'product_inquiry']]
            ]
        ];
    }

    public function dispatchMessage(MessageEvent $event): void
    {
        $subscribers = $this->eventSubscribers['message.received'] ?? [];

        // Sort by priority
        uasort($subscribers, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });

        foreach ($subscribers as $flowName => $config) {
            $flow = $this->getFlowByName($flowName);

            if ($this->shouldProcessEvent($flow, $event, $config)) {
                $result = $flow->handleEvent($event);

                if ($result->shouldStopPropagation()) {
                    break;
                }

                if ($result->shouldDelegate()) {
                    $this->delegateToSlaveFlows($result->getDelegatedFlows(), $event);
                }
            }
        }
    }

    private function shouldProcessEvent(Flow $flow, MessageEvent $event, array $config): bool
    {
        if ($config['always'] ?? false) {
            return true;
        }

        $conditions = $config['conditions'] ?? [];
        foreach ($conditions as $key => $value) {
            if ($key === 'intent' && $event->getIntent() !== $value) {
                return false;
            }
        }

        return true;
    }

    private function delegateToSlaveFlows(array $slaveFlows, MessageEvent $event): void
    {
        foreach ($slaveFlows as $slaveFlow) {
            $slaveFlow->handleEvent($event);
        }
    }
}

class MessageEvent
{
    private $message;
    private $contact;
    private $intent;
    private $context;

    public function __construct($message, Contact $contact, $intent = null, $context = [])
    {
        $this->message = $message;
        $this->contact = $contact;
        $this->intent = $intent;
        $this->context = $context;
    }

    public function getMessage() { return $this->message; }
    public function getContact() { return $this->contact; }
    public function getIntent() { return $this->intent; }
    public function getContext() { return $this->context; }
}

class FlowEventResult
{
    private $handled = false;
    private $stopPropagation = false;
    private $delegatedFlows = [];

    public function setHandled(bool $handled): self
    {
        $this->handled = $handled;
        return $this;
    }

    public function setStopPropagation(bool $stop): self
    {
        $this->stopPropagation = $stop;
        return $this;
    }

    public function setDelegatedFlows(array $flows): self
    {
        $this->delegatedFlows = $flows;
        return $this;
    }

    public function shouldStopPropagation(): bool { return $this->stopPropagation; }
    public function shouldDelegate(): bool { return !empty($this->delegatedFlows); }
    public function getDelegatedFlows(): array { return $this->delegatedFlows; }
}
```

**Technical Details**:
- Create MessageEvent class for event data
- Flows implement EventHandler interface
- Master flow acts as event coordinator
- Slave flows subscribe to specific event types
- Support for event propagation control

**Pros**:
- Decoupled architecture
- Easy to add new flows
- Event-driven scalability
- Flexible subscription model
- Clean separation of concerns

**Cons**:
- More complex event handling
- Potential for event loops
- Harder to debug
- Performance overhead
- Learning curve for developers

### Approach 6: Middleware-Based Flow Pipeline

**Concept**: Create a middleware pipeline where master flow acts as the first middleware.

**Implementation**:
```php
interface FlowMiddleware
{
    public function handle(MessageRequest $request, Closure $next): FlowResponse;
}

class MasterFlowMiddleware implements FlowMiddleware
{
    public function handle(MessageRequest $request, Closure $next): FlowResponse
    {
        // Master flow processing
        $intent = $this->classifyIntent($request->getMessage());
        $request->setIntent($intent);

        // Decide if we should delegate or handle
        if ($this->shouldDelegate($intent)) {
            $request->setDelegationTarget($this->findTargetFlow($intent));
        }

        return $next($request);
    }
}

class IntentClassificationMiddleware implements FlowMiddleware
{
    public function handle(MessageRequest $request, Closure $next): FlowResponse
    {
        if (!$request->hasIntent()) {
            $intent = $this->intentClassifier->classifyIntent(
                $request->getMessage(),
                $request->getContact()
            );
            $request->setIntent($intent);
        }

        return $next($request);
    }
}

class SlaveFlowSelectionMiddleware implements FlowMiddleware
{
    public function handle(MessageRequest $request, Closure $next): FlowResponse
    {
        if ($request->hasDelegationTarget()) {
            $targetFlow = $request->getDelegationTarget();
            $request->setSelectedFlow($targetFlow);
        } else {
            $flow = $this->selectFlowByIntent($request->getIntent(), $request->getContact());
            $request->setSelectedFlow($flow);
        }

        return $next($request);
    }
}

class FlowExecutionMiddleware implements FlowMiddleware
{
    public function handle(MessageRequest $request, Closure $next): FlowResponse
    {
        $flow = $request->getSelectedFlow();
        if ($flow) {
            $result = $flow->execute($request->getMessage(), $request->getContact());
            return new FlowResponse($result);
        }

        return new FlowResponse(['error' => 'No flow selected']);
    }
}

class FlowPipeline
{
    private $middlewares = [];

    public function __construct()
    {
        $this->middlewares = [
            new MasterFlowMiddleware(),
            new IntentClassificationMiddleware(),
            new SlaveFlowSelectionMiddleware(),
            new FlowExecutionMiddleware()
        ];
    }

    public function process(MessageRequest $request): FlowResponse
    {
        $pipeline = array_reduce(
            array_reverse($this->middlewares),
            function ($next, $middleware) {
                return function ($request) use ($middleware, $next) {
                    return $middleware->handle($request, $next);
                };
            },
            function ($request) {
                return new FlowResponse(['error' => 'Pipeline completed without result']);
            }
        );

        return $pipeline($request);
    }
}

class MessageRequest
{
    private $message;
    private $contact;
    private $intent;
    private $delegationTarget;
    private $selectedFlow;

    public function __construct($message, Contact $contact)
    {
        $this->message = $message;
        $this->contact = $contact;
    }

    // Getters and setters
    public function getMessage() { return $this->message; }
    public function getContact() { return $this->contact; }
    public function getIntent() { return $this->intent; }
    public function setIntent($intent) { $this->intent = $intent; }
    public function hasIntent() { return $this->intent !== null; }
    public function setDelegationTarget($target) { $this->delegationTarget = $target; }
    public function getDelegationTarget() { return $this->delegationTarget; }
    public function hasDelegationTarget() { return $this->delegationTarget !== null; }
    public function setSelectedFlow($flow) { $this->selectedFlow = $flow; }
    public function getSelectedFlow() { return $this->selectedFlow; }
}
```

**Technical Details**:
- Create middleware interfaces
- Master flow as first middleware
- Each middleware can modify or stop the pipeline
- Clean separation of concerns
- Easy to add/remove processing steps

**Pros**:
- Clean architecture
- Easy to add/remove steps
- Testable components
- Flexible processing pipeline
- Reusable middleware

**Cons**:
- More complex setup
- Middleware overhead
- Learning curve
- Potential performance impact
- Over-engineering for simple cases

## Comparison Matrix of All Approaches

| Approach | Complexity | Visual | Performance | Scalability | Flexibility | Maintenance |
|----------|------------|--------|-------------|-------------|-------------|-------------|
| **1. Master Flow Router** | Medium | High | Good | Good | High | Easy |
| **2. Service-Based Router** | Low | Medium | Excellent | Good | Medium | Easy |
| **3. Hierarchical System** | High | Medium | Good | Excellent | High | Complex |
| **4. Intent Orchestration** | Medium | Low | Good | Good | Excellent | Medium |
| **5. Event-Driven System** | High | Low | Good | Excellent | Excellent | Complex |
| **6. Middleware Pipeline** | High | Low | Medium | Good | Excellent | Medium |

## Implementation Recommendations by Use Case

### For Small to Medium Businesses (< 1000 conversations/day)
**Recommended**: **Approach 2 - Service-Based Master Router**
- Quick to implement
- Builds on existing code
- Easy to understand and maintain
- Sufficient flexibility for most use cases

### For Large Enterprises (> 1000 conversations/day)
**Recommended**: **Approach 1 - Master Flow Router** + **Approach 4 - Intent Orchestration**
- Visual flow management for business users
- AI-driven intelligent routing
- Scalable architecture
- Easy to modify routing rules

### For Complex Multi-Department Organizations
**Recommended**: **Approach 3 - Hierarchical System** + **Approach 5 - Event-Driven**
- Multi-level flow hierarchies
- Department-specific flows
- Event-driven scalability
- Complex routing capabilities

### For High-Performance Requirements
**Recommended**: **Approach 6 - Middleware Pipeline** + **Approach 2 - Service-Based**
- Optimized processing pipeline
- Minimal overhead
- Cacheable components
- High throughput

## Migration Strategy

### Phase 1: Foundation (Weeks 1-2)
1. Implement **Approach 2 - Service-Based Master Router**
2. Add database fields (is_master, master_flow_id, delegation_rules)
3. Enhance ConversationStateManager
4. Create basic master-slave flow relationships

### Phase 2: Intelligence (Weeks 3-4)
1. Enhance AI intent classification
2. Implement **Approach 4 - Intent Orchestration** features
3. Add dynamic routing rules
4. Create intent-to-flow mapping

### Phase 3: Visualization (Weeks 5-6)
1. Add **Approach 1 - Master Flow Router** visual components
2. Create custom node types (AIIntentNode, FlowRouterNode)
3. Build flow builder UI enhancements
4. Add visual routing configuration

### Phase 4: Advanced Features (Weeks 7-8)
1. Implement chosen advanced approach (Hierarchical/Event-Driven/Middleware)
2. Add analytics and monitoring
3. Performance optimization
4. A/B testing capabilities

## Technical Architecture Decision Tree

```
Start: What's your primary requirement?

├── Visual Flow Management?
│   ├── Yes → Approach 1 (Master Flow Router)
│   └── No → Continue
│
├── Quick Implementation?
│   ├── Yes → Approach 2 (Service-Based Router)
│   └── No → Continue
│
├── Complex Hierarchies?
│   ├── Yes → Approach 3 (Hierarchical System)
│   └── No → Continue
│
├── AI-First Routing?
│   ├── Yes → Approach 4 (Intent Orchestration)
│   └── No → Continue
│
├── High Scalability?
│   ├── Yes → Approach 5 (Event-Driven System)
│   └── No → Continue
│
└── Maximum Flexibility?
    └── Yes → Approach 6 (Middleware Pipeline)
```

## Risk Assessment and Mitigation

### Common Risks Across All Approaches

1. **AI Classification Accuracy**
   - **Risk**: Misclassified intents leading to wrong flow routing
   - **Mitigation**: Implement confidence thresholds, keyword fallbacks, human escalation

2. **Performance Degradation**
   - **Risk**: Additional processing overhead
   - **Mitigation**: Caching, async processing, performance monitoring

3. **Complexity Creep**
   - **Risk**: Over-engineering simple use cases
   - **Mitigation**: Start simple, iterate based on actual needs

4. **Maintenance Overhead**
   - **Risk**: Complex routing rules become hard to manage
   - **Mitigation**: Visual configuration tools, documentation, testing

### Approach-Specific Risks

#### Approach 1 (Master Flow Router)
- **Risk**: Custom node development complexity
- **Mitigation**: Reuse existing node patterns, thorough testing

#### Approach 2 (Service-Based Router)
- **Risk**: Hidden routing logic
- **Mitigation**: Comprehensive logging, admin dashboard

#### Approach 3 (Hierarchical System)
- **Risk**: Circular dependencies
- **Mitigation**: Validation rules, hierarchy depth limits

#### Approach 4 (Intent Orchestration)
- **Risk**: AI model dependency
- **Mitigation**: Multiple AI providers, offline fallbacks

#### Approach 5 (Event-Driven System)
- **Risk**: Event loops and debugging complexity
- **Mitigation**: Event tracing, circuit breakers

#### Approach 6 (Middleware Pipeline)
- **Risk**: Over-abstraction
- **Mitigation**: Clear middleware responsibilities, documentation

## Practical Implementation Example

### Scenario: Customer Service System

**Master Flow**: "Customer Service Router"
**Slave Flows**: 
1. "Technical Support Flow"
2. "Billing Support Flow" 
3. "Sales Inquiry Flow"
4. "General Information Flow"

### Message Examples and Routing:

1. **Message**: "Hi, I need help with my billing"
   - **Intent Detected**: `agent_request` + `billing` keywords
   - **Route To**: Billing Support Flow
   - **Flow Triggered**: Billing Support Flow starts execution

2. **Message**: "I want to buy your premium package"
   - **Intent Detected**: `product_inquiry` + `purchase` keywords
   - **Route To**: Sales Inquiry Flow
   - **Flow Triggered**: Sales Inquiry Flow starts execution

3. **Message**: "Hello, what services do you offer?"
   - **Intent Detected**: `company_info`
   - **Route To**: General Information Flow
   - **Flow Triggered**: General Information Flow starts execution

## Flow Creation with AI Intent Triggers

### Step 1: Create Master Flow

**Master Flow Configuration**:
```json
{
  "name": "Master Customer Service Router",
  "priority": 1,
  "is_active": true,
  "is_master": true,
  "flow_type": "master",
  "trigger_conditions": {
    "always_trigger": true
  }
}
```

**Master Flow Nodes**:
1. **Start Node** (`incomingMessage`): Receives all messages
2. **AI Intent Node** (`openai`): Classifies message intent
3. **Branch Node** (`branch`): Routes based on intent classification
4. **Trigger Nodes** (`webhook`): Triggers appropriate slave flows

### Step 2: Create Slave Flows

**Technical Support Flow**:
```json
{
  "name": "Technical Support Flow",
  "priority": 10,
  "is_active": true,
  "is_master": false,
  "master_flow_id": 1,
  "flow_type": "support",
  "trigger_conditions": {
    "intent": ["agent_request", "technical_support"],
    "keywords": ["technical", "bug", "error", "not working"]
  }
}
```

**Sales Inquiry Flow**:
```json
{
  "name": "Sales Inquiry Flow", 
  "priority": 10,
  "is_active": true,
  "is_master": false,
  "master_flow_id": 1,
  "flow_type": "sales",
  "trigger_conditions": {
    "intent": ["product_inquiry", "purchase_intent"],
    "keywords": ["buy", "purchase", "price", "cost", "premium"]
  }
}
```

### Step 3: Configure Intent-Based Routing

**Master Flow Branch Node Configuration**:
```json
{
  "settings": {
    "conditions": [
      {
        "variableId": "detected_intent",
        "operator": "equals", 
        "value": "agent_request",
        "target_flow": "technical_support_flow"
      },
      {
        "variableId": "detected_intent",
        "operator": "equals",
        "value": "product_inquiry", 
        "target_flow": "sales_inquiry_flow"
      },
      {
        "variableId": "detected_intent",
        "operator": "equals",
        "value": "company_info",
        "target_flow": "general_info_flow"
      }
    ]
  }
}
```

## Configuration Examples

### Intent Classification Configuration
```json
{
  "intent_mapping": {
    "agent_request": {
      "target_flows": ["technical_support_flow", "billing_support_flow"],
      "priority": 1,
      "keywords": ["agent", "human", "support", "help", "problem"],
      "ai_confidence_threshold": 0.8
    },
    "product_inquiry": {
      "target_flows": ["sales_inquiry_flow", "demo_flow"],
      "priority": 2, 
      "keywords": ["buy", "purchase", "price", "product", "service"],
      "ai_confidence_threshold": 0.7
    },
    "group_assignment": {
      "target_flows": ["membership_flow", "vip_flow"],
      "priority": 3,
      "keywords": ["join", "group", "vip", "premium", "membership"],
      "ai_confidence_threshold": 0.7
    }
  }
}
```

### Flow Delegation Rules
```json
{
  "delegation_rules": {
    "business_hours": {
      "condition": "{{current_time}} between 09:00 and 18:00",
      "true_flow": "live_support_flow",
      "false_flow": "after_hours_flow"
    },
    "user_type": {
      "condition": "{{user_group}} equals 'premium'",
      "true_flow": "premium_support_flow", 
      "false_flow": "standard_support_flow"
    }
  }
}
```

## Benefits and Recommendations

### Benefits of Master-Slave Architecture
1. **Centralized Control**: All messages processed through master flow
2. **Intelligent Routing**: AI-powered decision making
3. **Context Preservation**: Maintain conversation state across flows
4. **Scalability**: Easy to add new slave flows
5. **Flexibility**: Dynamic routing rules
6. **Monitoring**: Central analytics point
7. **Fallback Handling**: Graceful degradation

### Recommended Implementation
Start with **Enhanced Service-Based Router (Approach 2)**:
- Builds on existing architecture
- Requires minimal changes  
- Provides immediate benefits
- Can be enhanced incrementally

### Next Steps
1. Implement MasterSlaveFlowRouter service
2. Add database fields (is_master, master_flow_id, delegation_rules)
3. Enhance ConversationStateManager
4. Create master flow with intent classification
5. Configure slave flows with specific triggers
6. Test with real customer scenarios
7. Monitor and optimize routing accuracy

This architecture enables true master-slave flow operation where the master flow intelligently routes messages to appropriate slave flows based on AI intent classification, providing a robust and scalable customer service automation system.

---

## Detailed AI Intent Classification Example

### Real-World Scenario: E-commerce Customer Service

Let's walk through a complete example of how AI intent classification works with actual customer messages:

#### Customer Message Examples:

**Example 1: Technical Support Request**
```
Customer Message: "Hi, I'm having trouble logging into my account. The password reset isn't working."

AI Classification Process:
1. Keyword Analysis: "trouble", "password reset", "not working" → Technical issue detected
2. AI Analysis: Context suggests authentication problem requiring technical assistance
3. Intent Classification: "agent_request" with sub-category "technical_support"
4. Confidence Score: 0.92
5. Routing Decision: Route to "Technical Support Flow"
```

**Example 2: Sales Inquiry**
```
Customer Message: "I'm interested in your premium plan. What's included and how much does it cost?"

AI Classification Process:
1. Keyword Analysis: "premium plan", "cost", "interested" → Sales inquiry detected
2. AI Analysis: Customer showing purchase intent, asking about pricing
3. Intent Classification: "product_inquiry" with sub-category "pricing_request"
4. Confidence Score: 0.89
5. Routing Decision: Route to "Sales Inquiry Flow"
```

**Example 3: Group Assignment Request**
```
Customer Message: "Can you add me to the VIP group? I want access to exclusive features."

AI Classification Process:
1. Keyword Analysis: "add me", "VIP group", "exclusive" → Group membership detected
2. AI Analysis: User requesting group membership upgrade
3. Intent Classification: "group_assignment" with sub-category "vip_upgrade"
4. Confidence Score: 0.95
5. Routing Decision: Route to "VIP Membership Flow"
```

### Flow Node Structure for AI Intent Classification

#### Master Flow Node Sequence:

1. **Incoming Message Node** (`incomingMessage`)
   ```json
   {
     "type": "incomingMessage",
     "settings": {
       "trigger_on": "all_messages"
     }
   }
   ```

2. **AI Intent Classification Node** (`openai`)
   ```json
   {
     "type": "openai",
     "settings": {
       "prompt": "Classify this customer message into one of these intents: agent_request, product_inquiry, group_assignment, company_info, greeting, general_inquiry. Return JSON with intent and confidence.",
       "model": "gpt-4o-mini",
       "temperature": 0.1,
       "save_to_variable": "detected_intent"
     }
   }
   ```

3. **Branch Node for Intent Routing** (`branch`)
   ```json
   {
     "type": "branch",
     "settings": {
       "conditions": [
         {
           "variableId": "detected_intent",
           "operator": "equals",
           "value": "agent_request"
         }
       ]
     }
   }
   ```

4. **Webhook Nodes for Flow Triggering** (`webhook`)
   ```json
   {
     "type": "webhook",
     "settings": {
       "url": "/api/trigger-flow/technical-support",
       "method": "POST",
       "payload": {
         "contact_id": "{{contact_id}}",
         "original_message": "{{message}}",
         "detected_intent": "{{detected_intent}}"
       }
     }
   }
   ```

### Complete Flow Creation Example

#### Step-by-Step Flow Builder Process:

**1. Create Master Flow:**
- Name: "AI Customer Service Router"
- Priority: 1 (highest)
- Flow Type: "master"
- Is Active: true

**2. Add Nodes to Master Flow:**

```
[Start] → [AI Intent] → [Branch] → [Route to Slave Flows]
   ↓           ↓           ↓              ↓
Incoming    Classify    Decision      Trigger
Message     Intent      Point         Slaves
```

**3. Configure AI Intent Node:**
```json
{
  "node_type": "openai",
  "prompt": "You are a customer service intent classifier. Analyze this message and return JSON with 'intent' and 'confidence' fields. Available intents: agent_request, product_inquiry, group_assignment, company_info, greeting, general_inquiry.",
  "input_variable": "{{message}}",
  "output_variable": "detected_intent"
}
```

**4. Configure Branch Node Conditions:**
```json
{
  "conditions": [
    {
      "if": "{{detected_intent.intent}} == 'agent_request'",
      "then": "route_to_support"
    },
    {
      "if": "{{detected_intent.intent}} == 'product_inquiry'",
      "then": "route_to_sales"
    },
    {
      "if": "{{detected_intent.intent}} == 'group_assignment'",
      "then": "route_to_membership"
    }
  ],
  "default": "route_to_general"
}
```

**5. Create Slave Flows:**

**Technical Support Flow:**
```json
{
  "name": "Technical Support Flow",
  "trigger_conditions": {
    "intent": "agent_request",
    "triggered_by_master": true
  },
  "nodes": [
    {
      "type": "message",
      "content": "I understand you're having a technical issue. Let me connect you with our technical support team."
    },
    {
      "type": "assign_agent",
      "department": "technical_support"
    }
  ]
}
```

**Sales Inquiry Flow:**
```json
{
  "name": "Sales Inquiry Flow",
  "trigger_conditions": {
    "intent": "product_inquiry",
    "triggered_by_master": true
  },
  "nodes": [
    {
      "type": "message",
      "content": "Great! I'd be happy to help you learn about our products and pricing."
    },
    {
      "type": "template",
      "template_name": "product_catalog"
    },
    {
      "type": "assign_agent",
      "department": "sales"
    }
  ]
}
```

### Intent Classification Accuracy Improvement

#### Training Data Examples:
```json
{
  "training_examples": {
    "agent_request": [
      "I need help with my order",
      "Can someone assist me?",
      "I have a problem with my account",
      "The app is not working properly"
    ],
    "product_inquiry": [
      "What plans do you offer?",
      "How much does the premium version cost?",
      "I want to upgrade my subscription",
      "Tell me about your features"
    ],
    "group_assignment": [
      "Add me to VIP group",
      "I want premium membership",
      "Can I join the beta program?",
      "How do I get exclusive access?"
    ]
  }
}
```

#### Confidence Thresholds:
```json
{
  "confidence_settings": {
    "high_confidence": 0.8,
    "medium_confidence": 0.6,
    "low_confidence": 0.4,
    "fallback_to_keywords": 0.4,
    "escalate_to_human": 0.2
  }
}
```

This comprehensive example shows exactly how to create flows that are triggered by AI intent classification, providing a clear path from message analysis to appropriate flow execution.
